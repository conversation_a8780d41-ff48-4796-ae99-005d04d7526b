<?php
/**
 * <PERSON><PERSON><PERSON> tra hook của module AddFundBonus
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON><PERSON> cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

// Ghi log
function writeLog($message) {
    echo $message . "<br>";
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [HOOK CHECK] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Kiểm tra hook của AddFundBonus</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeeba; }
        .btn { display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Kiểm tra hook của AddFundBonus</h1>";

writeLog("Bắt đầu kiểm tra hook...");

// Kiểm tra module
$moduleFile = ROOTDIR . '/modules/addons/addfundbonus/addfundbonus.php';
$moduleExists = file_exists($moduleFile);

if (!$moduleExists) {
    echo "<div class='alert alert-danger'>Module file không tồn tại tại đường dẫn: $moduleFile</div>";
    die("</div></body></html>");
}

// Kiểm tra hook file
$hookFile = ROOTDIR . '/includes/hooks/addfundbonus.php';
$hookExists = file_exists($hookFile);

if (!$hookExists) {
    echo "<div class='alert alert-danger'>Hook file không tồn tại: $hookFile</div>";
    echo "<div class='alert alert-warning'>Cần tạo file hook để module hoạt động!</div>";
    
    // Tạo file hook mẫu
    echo "<h2>Tạo file hook</h2>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='create_hook' value='1'>";
    echo "<input type='submit' value='Tạo file hook' class='btn'>";
    echo "</form>";
    
    if (isset($_POST['create_hook'])) {
        $hookContent = '<?php
/**
 * AddFundBonus Hook File
 */

use WHMCS\Database\Capsule;

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

/**
 * Hook vào sự kiện nạp tiền thành công
 */
add_hook("AddInvoicePayment", 1, function($vars) {
    // Log cho việc debug
    $logFile = ROOTDIR . \'/modules/addons/addfundbonus/logs/addfundbonus.log\';
    $timestamp = date(\'Y-m-d H:i:s\');
    
    try {
        // Kiểm tra xem module có được kích hoạt không
        $enabled = Capsule::table("tbladdonmodules")
            ->where("module", "addfundbonus")
            ->where("setting", "enabled")
            ->value("value");
            
        if ($enabled != "on") {
            return;
        }
        
        // Lấy dữ liệu hóa đơn
        $invoiceId = $vars[\'invoiceid\'];
        $invoice = Capsule::table("tblinvoices")->where("id", $invoiceId)->first();
        
        // Chỉ xử lý hóa đơn nạp tiền
        if (!$invoice || $invoice->status != "Paid") {
            return;
        }
        
        // Kiểm tra xem đây có phải là hóa đơn nạp tiền không
        $addFunds = Capsule::table("tblinvoiceitems")
            ->where("invoiceid", $invoiceId)
            ->where("type", "AddFunds")
            ->first();
            
        if (!$addFunds) {
            return;
        }
        
        // Lấy số tiền nạp
        $amount = $addFunds->amount;
        $clientId = $invoice->userid;
        
        // Log thông tin
        $logMessage = "[{$timestamp}] Phát hiện nạp tiền: Invoice #{$invoiceId}, Client #{$clientId}, Amount: {$amount}\n\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);
        
        // Tìm rule phù hợp
        $rule = Capsule::table("mod_addfundbonus_rules")
            ->where("active", 1)
            ->where(function ($query) use ($amount) {
                $query->where(function ($q) use ($amount) {
                    $q->where("min_amount", "<=", $amount)
                      ->where("max_amount", ">=", $amount);
                })
                ->orWhere(function ($q) use ($amount) {
                    $q->where("min_amount", "<=", $amount)
                      ->where("max_amount", 0);
                });
            })
            ->orderBy("bonus_percent", "desc")
            ->first();
            
        if (!$rule) {
            // Nếu không có rule nào phù hợp, sử dụng bonus mặc định
            $defaultBonus = Capsule::table("tbladdonmodules")
                ->where("module", "addfundbonus")
                ->where("setting", "default_bonus_percent")
                ->value("value");
                
            $bonusPercent = $defaultBonus ? (float)$defaultBonus : 0;
            $ruleId = null;
        } else {
            $bonusPercent = $rule->bonus_percent;
            $ruleId = $rule->id;
        }
        
        // Tính số tiền thưởng
        if ($bonusPercent > 0) {
            $bonusAmount = $amount * ($bonusPercent / 100);
            
            // Log thông tin
            $logMessage = "[{$timestamp}] Áp dụng khuyến mãi: {$bonusPercent}%, Số tiền thưởng: {$bonusAmount}\n\n";
            file_put_contents($logFile, $logMessage, FILE_APPEND);
            
            // Thêm tiền vào tài khoản
            $currentCredit = Capsule::table("tblclients")
                ->where("id", $clientId)
                ->value("credit");
                
            Capsule::table("tblclients")
                ->where("id", $clientId)
                ->update([
                    "credit" => $currentCredit + $bonusAmount
                ]);
                
            // Ghi log giao dịch
            Capsule::table("mod_addfundbonus_logs")->insert([
                "client_id" => $clientId,
                "transaction_id" => $invoiceId,
                "amount" => $amount,
                "bonus_percent" => $bonusPercent,
                "bonus_amount" => $bonusAmount,
                "rule_id" => $ruleId,
                "created_at" => date("Y-m-d H:i:s")
            ]);
            
            // Ghi log hệ thống
            addcredit(
                $clientId,
                $bonusAmount,
                "Thưởng nạp tiền " . $bonusPercent . "%",
                0,
                0,
                $invoiceId
            );
            
            $logMessage = "[{$timestamp}] Đã thêm {$bonusAmount} vào tài khoản khách hàng #{$clientId}\n\n";
            file_put_contents($logFile, $logMessage, FILE_APPEND);
        }
        
    } catch (Exception $e) {
        // Log lỗi
        $logMessage = "[{$timestamp}] Lỗi khi xử lý khuyến mãi: " . $e->getMessage() . "\n";
        $logMessage .= "Stack trace: " . $e->getTraceAsString() . "\n\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);
    }
});
';
        
        // Tạo thư mục nếu chưa có
        $hookDir = ROOTDIR . '/includes/hooks';
        if (!is_dir($hookDir)) {
            mkdir($hookDir, 0755, true);
        }
        
        // Ghi file
        file_put_contents($hookFile, $hookContent);
        writeLog("Đã tạo file hook: $hookFile");
        
        echo "<div class='alert alert-success'>Đã tạo file hook thành công!</div>";
        echo "<div class='alert alert-info'>Hãy thử nạp tiền lại sau khi làm mới hệ thống!</div>";
    }
} else {
    echo "<div class='alert alert-success'>Hook file đã tồn tại: $hookFile</div>";
    
    // Hiển thị nội dung file hook
    $hookContent = file_get_contents($hookFile);
    echo "<h2>Nội dung file hook:</h2>";
    echo "<pre>" . htmlspecialchars($hookContent) . "</pre>";
    
    // Kiểm tra xem hook có chứa các hàm cần thiết không
    $hasAddInvoicePaymentHook = strpos($hookContent, 'add_hook("AddInvoicePayment"') !== false;
    
    if (!$hasAddInvoicePaymentHook) {
        echo "<div class='alert alert-warning'>Hook file không chứa hook AddInvoicePayment!</div>";
        echo "<div class='alert alert-info'>Hook này cần thiết để xử lý khuyến mãi khi nạp tiền.</div>";
    } else {
        echo "<div class='alert alert-success'>Hook file chứa hook AddInvoicePayment.</div>";
    }
    
    // Kiểm tra gần đây có ai nạp tiền không
    try {
        $recentLogs = Capsule::table('mod_addfundbonus_logs')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
            
        if (count($recentLogs) > 0) {
            echo "<h2>Giao dịch khuyến mãi gần đây:</h2>";
            echo "<table style='width: 100%; border-collapse: collapse;'>";
            echo "<tr style='background-color: #f2f2f2;'>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>ID</th>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>Client ID</th>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>Số tiền nạp</th>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>Bonus %</th>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>Số tiền thưởng</th>";
            echo "<th style='padding: 8px; border: 1px solid #ddd;'>Thời gian</th>";
            echo "</tr>";
            
            foreach ($recentLogs as $log) {
                echo "<tr>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->id}</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->client_id}</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->amount}</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->bonus_percent}%</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->bonus_amount}</td>";
                echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$log->created_at}</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<div class='alert alert-info'>Chưa có giao dịch khuyến mãi nào được ghi nhận.</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Lỗi khi lấy dữ liệu logs: " . $e->getMessage() . "</div>";
    }
    
    // Form để test khuyến mãi
    echo "<h2>Kiểm tra rule áp dụng cho số tiền:</h2>";
    echo "<form method='post'>";
    echo "<input type='number' name='test_amount' placeholder='Nhập số tiền cần kiểm tra' step='0.01' required style='padding: 10px; margin-right: 10px;'>";
    echo "<input type='submit' value='Kiểm tra' class='btn'>";
    echo "</form>";
    
    if (isset($_POST['test_amount'])) {
        $testAmount = (float)$_POST['test_amount'];
        
        try {
            // Tìm rule phù hợp
            $rule = Capsule::table("mod_addfundbonus_rules")
                ->where("active", 1)
                ->where(function ($query) use ($testAmount) {
                    $query->where(function ($q) use ($testAmount) {
                        $q->where("min_amount", "<=", $testAmount)
                          ->where("max_amount", ">=", $testAmount);
                    })
                    ->orWhere(function ($q) use ($testAmount) {
                        $q->where("min_amount", "<=", $testAmount)
                          ->where("max_amount", 0);
                    });
                })
                ->orderBy("bonus_percent", "desc")
                ->first();
                
            if ($rule) {
                $bonusPercent = $rule->bonus_percent;
                $bonusAmount = $testAmount * ($bonusPercent / 100);
                
                echo "<div class='alert alert-success'>";
                echo "Số tiền: {$testAmount} sẽ được áp dụng khuyến mãi {$bonusPercent}%<br>";
                echo "Số tiền thưởng: {$bonusAmount}<br>";
                echo "Tổng số tiền: " . ($testAmount + $bonusAmount);
                echo "</div>";
            } else {
                // Nếu không có rule nào phù hợp, sử dụng bonus mặc định
                $defaultBonus = Capsule::table("tbladdonmodules")
                    ->where("module", "addfundbonus")
                    ->where("setting", "default_bonus_percent")
                    ->value("value");
                    
                $bonusPercent = $defaultBonus ? (float)$defaultBonus : 0;
                
                if ($bonusPercent > 0) {
                    $bonusAmount = $testAmount * ($bonusPercent / 100);
                    
                    echo "<div class='alert alert-info'>";
                    echo "Không có rule cụ thể, sử dụng bonus mặc định: {$bonusPercent}%<br>";
                    echo "Số tiền thưởng: {$bonusAmount}<br>";
                    echo "Tổng số tiền: " . ($testAmount + $bonusAmount);
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-warning'>Không tìm thấy rule phù hợp cho số tiền: {$testAmount}</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='alert alert-danger'>Lỗi khi kiểm tra: " . $e->getMessage() . "</div>";
        }
    }
}

// Hiển thị quy tắc hiện tại
try {
    $rules = Capsule::table('mod_addfundbonus_rules')->get();
    
    echo "<h2>Danh sách quy tắc hiện tại:</h2>";
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background-color: #f2f2f2;'>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>ID</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Min</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Max</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Bonus %</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Mô tả</th>";
    echo "<th style='padding: 8px; border: 1px solid #ddd;'>Trạng thái</th>";
    echo "</tr>";
    
    foreach ($rules as $rule) {
        echo "<tr>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$rule->id}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$rule->min_amount}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($rule->max_amount > 0 ? $rule->max_amount : 'Không giới hạn') . "</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$rule->bonus_percent}%</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>{$rule->description}</td>";
        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . ($rule->active ? 'Kích hoạt' : 'Tắt') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>Lỗi khi lấy dữ liệu rules: " . $e->getMessage() . "</div>";
}

echo "
        <div style='margin-top: 20px;'>
            <a href='fix_module.php' class='btn'>Quay lại công cụ sửa lỗi</a>
            <a href='direct_update.php' class='btn'>Quản lý rules</a>
            <a href='addonmodules.php?module=addfundbonus' class='btn'>Đi đến module</a>
        </div>
    </div>
</body>
</html>";
?> 