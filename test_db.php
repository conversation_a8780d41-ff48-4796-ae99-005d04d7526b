<?php
/**
 * Database structure test for AddFundBonus module
 * Place this file in the root directory and access it from browser to check database status
 */

// Initialize WHMCS
require_once 'init.php';

// Check if user is admin
if (!isset($_SESSION['adminid'])) {
    die("Access Denied: Admin login required");
}

use Illuminate\Database\Capsule\Manager as Capsule;

echo "<h1>AddFundBonus Database Structure Test</h1>";

// Function to log results
function logTestResult($message) {
    echo $message . "<br>";
    
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [DB TEST] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

try {
    // Check if tables exist
    $tables = [
        'mod_addfundbonus_rules',
        'mod_addfundbonus_logs'
    ];
    
    echo "<h2>Checking Tables</h2>";
    
    foreach ($tables as $table) {
        $exists = Capsule::schema()->hasTable($table);
        if ($exists) {
            logTestResult("✅ Table '{$table}' exists");
            
            // Show table structure
            $columns = Capsule::schema()->getColumnListing($table);
            echo "<h3>Structure of '{$table}':</h3>";
            echo "<ul>";
            foreach ($columns as $column) {
                $type = Capsule::connection()->getDoctrineColumn($table, $column)->getType()->getName();
                echo "<li>{$column} ({$type})</li>";
            }
            echo "</ul>";
            
            // Count records
            $count = Capsule::table($table)->count();
            logTestResult("Table '{$table}' has {$count} records");
            
            // If rules table and has records, show the first few records
            if ($table == 'mod_addfundbonus_rules' && $count > 0) {
                $rules = Capsule::table($table)->limit(5)->get();
                echo "<h4>Sample Rules:</h4>";
                echo "<pre>";
                print_r($rules);
                echo "</pre>";
            }
        } else {
            logTestResult("❌ Table '{$table}' does not exist");
        }
    }
    
    // Check module settings
    echo "<h2>Checking Module Settings</h2>";
    $settings = Capsule::table('tbladdonmodules')
        ->where('module', 'addfundbonus')
        ->get();
        
    if (count($settings) > 0) {
        logTestResult("✅ Module settings found");
        echo "<h3>Module Settings:</h3>";
        echo "<ul>";
        foreach ($settings as $setting) {
            echo "<li>{$setting->setting}: {$setting->value}</li>";
        }
        echo "</ul>";
    } else {
        logTestResult("❌ No module settings found. Is the module activated?");
    }
    
    // Check log file
    echo "<h2>Checking Log File</h2>";
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    
    if (file_exists($logFile)) {
        $fileSize = filesize($logFile);
        $fileSizeFormatted = round($fileSize / 1024, 2) . ' KB';
        $isWritable = is_writable($logFile);
        logTestResult("✅ Log file exists. Size: {$fileSizeFormatted}, Writable: " . ($isWritable ? 'Yes' : 'No'));
        
        // Show last few lines of log
        $lastLines = file_get_contents($logFile, false, null, -min(5000, $fileSize));
        echo "<h3>Last log entries:</h3>";
        echo "<pre style='max-height: 300px; overflow: auto;'>";
        echo htmlspecialchars($lastLines);
        echo "</pre>";
    } else {
        logTestResult("❌ Log file does not exist");
        
        // Try to create it
        $content = "[" . date('Y-m-d H:i:s') . "] Log file created by test_db.php\n\n";
        $created = file_put_contents($logFile, $content);
        if ($created) {
            logTestResult("✅ Created log file successfully");
        } else {
            logTestResult("❌ Failed to create log file. Check directory permissions.");
        }
    }
    
    // Test hook registration
    echo "<h2>Checking Hook Registration</h2>";
    $hookFile = ROOTDIR . '/modules/addons/addfundbonus/hooks/addfunds.php';
    
    if (file_exists($hookFile)) {
        logTestResult("✅ Hook file exists");
        
        // Check if it's properly included
        $hookContents = file_get_contents($hookFile);
        if (strpos($hookContents, "add_hook('AddFunds'") !== false) {
            logTestResult("✅ Hook file contains AddFunds hook registration");
        } else {
            logTestResult("❌ Hook file does not contain proper hook registration");
        }
    } else {
        logTestResult("❌ Hook file does not exist");
    }
    
    echo "<p><strong>Test completed.</strong> Check the logs in your module for more details.</p>";
    
} catch (Exception $e) {
    logTestResult("❌ Error: " . $e->getMessage());
    echo "<pre>";
    print_r($e->getTraceAsString());
    echo "</pre>";
}
?> 