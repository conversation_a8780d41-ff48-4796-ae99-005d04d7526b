<?php
/**
 * Kiểm tra database đơn giản không dùng Doctrine
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON><PERSON> cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

echo "<h1>Kiểm tra AddFundBonus (Phiên bản đơn giản)</h1>";

// Ghi log
function writeLog($message) {
    echo $message . "<br>";
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [SIMPLE TEST] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

try {
    // Kiểm tra kết nối database
    writeLog("Đang kiểm tra kết nối database");
    
    // Kiểm tra bảng users để xác nhận kết nối
    $userCount = Capsule::table('tbladmins')->count();
    writeLog("Kết nối database OK. Đếm tbladmins: " . $userCount);
    
    // Kiểm tra bảng rules
    $rulesExists = false;
    try {
        $count = Capsule::table('mod_addfundbonus_rules')->count();
        $rulesExists = true;
        writeLog("Bảng mod_addfundbonus_rules tồn tại, có " . $count . " rules");
    } catch (Exception $e) {
        $rulesExists = false;
        writeLog("Bảng mod_addfundbonus_rules không tồn tại");
    }
    
    // Tạo bảng nếu chưa tồn tại
    if (!$rulesExists) {
        $sql = "CREATE TABLE IF NOT EXISTS `mod_addfundbonus_rules` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `min_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
            `max_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
            `bonus_percent` DECIMAL(5,2) NOT NULL DEFAULT '0.00',
            `description` VARCHAR(255) NOT NULL,
            `active` TINYINT(1) NOT NULL DEFAULT '1',
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;";
        
        // Thực thi truy vấn
        Capsule::connection()->statement($sql);
        writeLog("Đã tạo bảng mod_addfundbonus_rules");
    }
    
    // Kiểm tra bảng logs
    $logsExists = false;
    try {
        $count = Capsule::table('mod_addfundbonus_logs')->count();
        $logsExists = true;
        writeLog("Bảng mod_addfundbonus_logs tồn tại, có " . $count . " logs");
    } catch (Exception $e) {
        $logsExists = false;
        writeLog("Bảng mod_addfundbonus_logs không tồn tại");
    }
    
    // Tạo bảng logs nếu chưa tồn tại
    if (!$logsExists) {
        $sql = "CREATE TABLE IF NOT EXISTS `mod_addfundbonus_logs` (
            `id` INT(11) NOT NULL AUTO_INCREMENT,
            `client_id` INT(11) NOT NULL,
            `transaction_id` INT(11) NOT NULL,
            `amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
            `bonus_percent` DECIMAL(5,2) NOT NULL DEFAULT '0.00',
            `bonus_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
            `rule_id` INT(11) NULL,
            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;";
        
        // Thực thi truy vấn
        Capsule::connection()->statement($sql);
        writeLog("Đã tạo bảng mod_addfundbonus_logs");
    }
    
    // Kiểm tra rules
    $ruleCount = Capsule::table('mod_addfundbonus_rules')->count();
    
    // Hiển thị danh sách rules
    echo "<h2>Có $ruleCount rules trong hệ thống:</h2>";
    
    if ($ruleCount > 0) {
        // Lấy danh sách rules
        $rules = Capsule::table('mod_addfundbonus_rules')->get();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Min</th><th>Max</th><th>Bonus %</th><th>Mô tả</th><th>Trạng thái</th></tr>";
        
        foreach ($rules as $rule) {
            echo "<tr>";
            echo "<td>" . $rule->id . "</td>";
            echo "<td>" . $rule->min_amount . "</td>";
            echo "<td>" . ($rule->max_amount > 0 ? $rule->max_amount : 'Không giới hạn') . "</td>";
            echo "<td>" . $rule->bonus_percent . "%</td>";
            echo "<td>" . $rule->description . "</td>";
            echo "<td>" . ($rule->active ? 'Kích hoạt' : 'Tắt') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<div style='margin: 20px 0; padding: 10px; background-color: #fff3cd; border: 1px solid #ffeeba;'>";
        echo "Không có rule nào trong hệ thống. Bạn cần thêm rule.";
        echo "</div>";
        
        // Form thêm rule mẫu
        echo "<h3>Thêm Rules mẫu</h3>";
        echo "<form method='post' action='create_samples.php'>";
        echo "<button type='submit' style='padding: 10px; background-color: #007bff; color: white; border: none; cursor: pointer;'>Thêm 3 Rules mẫu</button>";
        echo "</form>";
    }
    
    // Kiểm tra module settings
    $moduleSettings = Capsule::table('tbladdonmodules')
        ->where('module', 'addfundbonus')
        ->get();
    
    echo "<h2>Trạng thái Module:</h2>";
    
    if (count($moduleSettings) > 0) {
        echo "<div style='margin: 20px 0; padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb;'>";
        echo "✅ Module addfundbonus đã được kích hoạt.";
        echo "</div>";
        
        echo "<h3>Cài đặt Module:</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Setting</th><th>Value</th></tr>";
        
        foreach ($moduleSettings as $setting) {
            echo "<tr>";
            echo "<td>" . $setting->setting . "</td>";
            echo "<td>" . $setting->value . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<div style='margin: 20px 0; padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb;'>";
        echo "❌ Module addfundbonus chưa được kích hoạt.";
        echo "</div>";
        
        echo "<p>Hãy vào <strong>Admin > Setup > Addon Modules</strong> để kích hoạt module.</p>";
    }
    
    // Tạo trang tạo rule
    echo "<h2>Công cụ:</h2>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='create_tables.php' style='display: inline-block; margin-right: 10px; padding: 10px; background-color: #28a745; color: white; text-decoration: none;'>Tạo bảng & sample</a>";
    echo "<a href='fix_module.php' style='display: inline-block; margin-right: 10px; padding: 10px; background-color: #17a2b8; color: white; text-decoration: none;'>Sửa lỗi module</a>";
    echo "<a href='addonmodules.php?module=addfundbonus' style='display: inline-block; padding: 10px; background-color: #007bff; color: white; text-decoration: none;'>Mở module</a>";
    echo "</div>";
    
} catch (Exception $e) {
    writeLog("Lỗi: " . $e->getMessage());
    echo "<div style='color: red; padding: 10px; border: 1px solid red;'>";
    echo "<strong>Lỗi:</strong> " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}
?> 