<?php
$license = 'DPTCLOUD.VN';
$db_host = 'localhost';
$db_port = '';
$db_username = 'whmcs';
$db_password = 'xKGzNZHXzMKAij4d';
$db_name = 'whmcs';
$db_tls_ca = '';
$db_tls_ca_path = '';
$db_tls_cert = '';
$db_tls_cipher = '';
$db_tls_key = '';
$db_tls_verify_cert = '';
$cc_encryption_hash = 'qIFxdVXt0w9szs1ygRP1Q8DAAfztD8cfk3q8UxtaW0qThiROaawfoNrK8nZVnBXK';
$mysql_charset = 'utf8';
$api_allowed_ip = ['**************', '**************'];
$systemurl = 'http://dptcloud.vn/client/';
$use_forwarded_for = true;
if (isset($_SERVER['HTTP_CF_CONNECTING_IP'])) {
    $_SERVER['REMOTE_ADDR'] = $_SERVER['HTTP_CF_CONNECTING_IP'];
} elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip_list = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
    $_SERVER['REMOTE_ADDR'] = trim($ip_list[0]);
}