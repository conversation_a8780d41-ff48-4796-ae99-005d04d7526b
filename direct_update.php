<?php
/**
 * <PERSON><PERSON>ng cụ cập nhật rule trực tiếp
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON><PERSON> cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

// Xử lý cập nhật
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_rule'])) {
    try {
        $ruleId = (int)$_POST['rule_id'];
        $minAmount = (float)$_POST['min_amount'];
        $maxAmount = (float)$_POST['max_amount'];
        $bonusPercent = (float)$_POST['bonus_percent'];
        $description = $_POST['description'];
        $active = isset($_POST['active']) ? 1 : 0;
        $startDate = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
        $endDate = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
        
        // Cập nhật rule trực tiếp bằng SQL
        $updateData = [
            'min_amount' => $minAmount,
            'max_amount' => $maxAmount,
            'bonus_percent' => $bonusPercent,
            'description' => $description,
            'active' => $active,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $updated = Capsule::table('mod_addfundbonus_rules')
            ->where('id', $ruleId)
            ->update($updateData);
            
        $message = $updated ? "Đã cập nhật rule ID: $ruleId thành công" : "Không có thay đổi cho rule ID: $ruleId";
        $alertType = $updated ? "alert-success" : "alert-warning";
    } catch (Exception $e) {
        $message = "Lỗi: " . $e->getMessage();
        $alertType = "alert-danger";
    }
}

// Xử lý thêm mới
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_rule'])) {
    try {
        $minAmount = (float)$_POST['min_amount'];
        $maxAmount = (float)$_POST['max_amount'];
        $bonusPercent = (float)$_POST['bonus_percent'];
        $description = $_POST['description'];
        $active = isset($_POST['active']) ? 1 : 0;
        $startDate = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
        $endDate = !empty($_POST['end_date']) ? $_POST['end_date'] : null;
        
        // Thêm rule mới
        $insertData = [
            'min_amount' => $minAmount,
            'max_amount' => $maxAmount,
            'bonus_percent' => $bonusPercent,
            'description' => $description,
            'active' => $active,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $newId = Capsule::table('mod_addfundbonus_rules')->insertGetId($insertData);
            
        $message = "Đã thêm rule mới ID: $newId thành công";
        $alertType = "alert-success";
    } catch (Exception $e) {
        $message = "Lỗi: " . $e->getMessage();
        $alertType = "alert-danger";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Cập nhật trực tiếp rule</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeeba; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-secondary { background: #6c757d; }
        input, select { padding: 8px; width: 100%; box-sizing: border-box; }
        .form-group { margin-bottom: 15px; }
        h1, h2 { color: #333; }
        .badge { display: inline-block; padding: 3px 7px; border-radius: 3px; font-size: 12px; }
        .badge-success { background-color: #28a745; color: white; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-warning { background-color: #ffc107; color: #212529; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cập nhật trực tiếp rule</h1>
        
        <?php if (isset($message)): ?>
        <div class="alert <?php echo $alertType; ?>"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <h2>Danh sách Rules</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>Min</th>
                <th>Max</th>
                <th>Bonus %</th>
                <th>Mô tả</th>
                <th>Thời gian hiệu lực</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
            </tr>
            <?php
            $rules = Capsule::table('mod_addfundbonus_rules')->get();
            $today = date('Y-m-d');
            
            foreach ($rules as $rule):
                // Xác định trạng thái hiệu lực
                $status = "Kích hoạt";
                $statusClass = "badge-success";
                
                if (!$rule->active) {
                    $status = "Tắt";
                    $statusClass = "badge-danger";
                } else {
                    if (!empty($rule->start_date) && $today < $rule->start_date) {
                        $status = "Chưa hiệu lực";
                        $statusClass = "badge-warning";
                    } else if (!empty($rule->end_date) && $today > $rule->end_date) {
                        $status = "Đã hết hạn";
                        $statusClass = "badge-danger";
                    }
                }
            ?>
            <tr>
                <td><?php echo $rule->id; ?></td>
                <td><?php echo $rule->min_amount; ?></td>
                <td><?php echo ($rule->max_amount > 0 ? $rule->max_amount : 'Không giới hạn'); ?></td>
                <td><?php echo $rule->bonus_percent; ?>%</td>
                <td><?php echo $rule->description; ?></td>
                <td>
                    <?php 
                    if (!empty($rule->start_date) || !empty($rule->end_date)) {
                        echo "Từ: " . (!empty($rule->start_date) ? $rule->start_date : 'Không giới hạn');
                        echo "<br>Đến: " . (!empty($rule->end_date) ? $rule->end_date : 'Không giới hạn');
                    } else {
                        echo "Không giới hạn";
                    }
                    ?>
                </td>
                <td><span class="badge <?php echo $statusClass; ?>"><?php echo $status; ?></span></td>
                <td><a href="?edit=<?php echo $rule->id; ?>" class="btn">Sửa</a></td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <?php
        if (isset($_GET['edit']) && is_numeric($_GET['edit'])):
            $ruleId = (int)$_GET['edit'];
            $ruleToEdit = Capsule::table('mod_addfundbonus_rules')
                ->where('id', $ruleId)
                ->first();
                
            if ($ruleToEdit):
        ?>
        <h2>Cập nhật Rule ID: <?php echo $ruleToEdit->id; ?></h2>
        <form method="post" action="">
            <input type="hidden" name="update_rule" value="1">
            <input type="hidden" name="rule_id" value="<?php echo $ruleToEdit->id; ?>">
            
            <div class="form-group">
                <label>Số tiền tối thiểu:</label>
                <input type="number" name="min_amount" step="0.01" value="<?php echo $ruleToEdit->min_amount; ?>" required>
            </div>
            
            <div class="form-group">
                <label>Số tiền tối đa (0 = không giới hạn):</label>
                <input type="number" name="max_amount" step="0.01" value="<?php echo $ruleToEdit->max_amount; ?>">
            </div>
            
            <div class="form-group">
                <label>Phần trăm thưởng (%):</label>
                <input type="number" name="bonus_percent" step="0.01" value="<?php echo $ruleToEdit->bonus_percent; ?>" required>
            </div>
            
            <div class="form-group">
                <label>Mô tả:</label>
                <input type="text" name="description" value="<?php echo htmlspecialchars($ruleToEdit->description); ?>" required>
            </div>
            
            <div class="form-group">
                <label>Ngày bắt đầu hiệu lực (để trống = không giới hạn):</label>
                <input type="date" name="start_date" value="<?php echo $ruleToEdit->start_date ?? ''; ?>">
            </div>
            
            <div class="form-group">
                <label>Ngày kết thúc hiệu lực (để trống = không giới hạn):</label>
                <input type="date" name="end_date" value="<?php echo $ruleToEdit->end_date ?? ''; ?>">
            </div>
            
            <div class="form-group">
                <label><input type="checkbox" name="active" <?php echo ($ruleToEdit->active ? 'checked' : ''); ?>> Kích hoạt</label>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">Cập nhật</button>
                <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn btn-secondary" style="margin-left: 10px;">Hủy</a>
            </div>
        </form>
        <?php 
            else:
                echo "<div class='alert alert-danger'>Không tìm thấy rule với ID: $ruleId</div>";
            endif;
        else:
        ?>
        
        <h2>Thêm Rule Mới</h2>
        <form method="post" action="">
            <input type="hidden" name="add_rule" value="1">
            
            <div class="form-group">
                <label>Số tiền tối thiểu:</label>
                <input type="number" name="min_amount" step="0.01" value="0" required>
            </div>
            
            <div class="form-group">
                <label>Số tiền tối đa (0 = không giới hạn):</label>
                <input type="number" name="max_amount" step="0.01" value="0">
            </div>
            
            <div class="form-group">
                <label>Phần trăm thưởng (%):</label>
                <input type="number" name="bonus_percent" step="0.01" value="0" required>
            </div>
            
            <div class="form-group">
                <label>Mô tả:</label>
                <input type="text" name="description" required>
            </div>
            
            <div class="form-group">
                <label>Ngày bắt đầu hiệu lực (để trống = không giới hạn):</label>
                <input type="date" name="start_date">
            </div>
            
            <div class="form-group">
                <label>Ngày kết thúc hiệu lực (để trống = không giới hạn):</label>
                <input type="date" name="end_date">
            </div>
            
            <div class="form-group">
                <label><input type="checkbox" name="active" checked> Kích hoạt</label>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">Thêm Rule</button>
            </div>
        </form>
        
        <?php endif; ?>
        
        <div style="margin-top: 20px;">
            <a href="fix_module.php" class="btn">Quay lại công cụ sửa lỗi</a>
            <a href="check_hook.php" class="btn">Kiểm tra hook</a>
            <a href="addonmodules.php?module=addfundbonus" class="btn">Đi đến module</a>
        </div>
    </div>
</body>
</html> 