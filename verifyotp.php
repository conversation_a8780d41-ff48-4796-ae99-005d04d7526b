<?php
require_once __DIR__ . '/init.php';
use WHMCS\Database\Capsule;

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Set header for JSON response
header('Content-Type: application/json');

function sendResponse($status, $message, $redirect = null) {
    $response = ['status' => $status, 'message' => $message];
    if ($redirect) {
        $response['redirect'] = $redirect;
    }
    session_write_close();
    echo json_encode($response);
    exit;
}

function sendToDiscordWebhook($title, $description, $color, $fields = []) {
    $webhookUrl = 'https://discord.com/api/webhooks/1347111685747376128/pCeFakAoNwl8HvvDDUET2ATwHgtH3M15nKz-9vh8DPGNKdATEmT-0DIiJk4ddxD83ooR';
    
    $timestamp = date('c');
    
    $embed = [
        'title' => $title,
        'description' => $description,
        'color' => $color,
        'timestamp' => $timestamp,
        'footer' => [
            'text' => 'OTP Verification System'
        ]
    ];
    
    if (!empty($fields)) {
        $embed['fields'] = $fields;
    }
    
    $data = [
        'username' => 'OTP Verification Bot',
        'embeds' => [$embed]
    ];
    
    $options = [
        'http' => [
            'header'  => "Content-Type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($data),
        ],
    ];
    
    $context = stream_context_create($options);
    @file_get_contents($webhookUrl, false, $context);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['otp'])) {
    $otpInput = trim($_POST['otp']);
    $clientId = $_SESSION['client_id'] ?? null;
    
    if (!$clientId) {
        logModuleCall('OTP Verification', 'CheckSession', 'No client ID in session', $_SESSION);
        sendResponse('error', 'Phiên đăng nhập không hợp lệ. Vui lòng thử lại.');
    }
    
    try {
        $client = Capsule::table('tblclients')->where('id', $clientId)->first();
        if (!$client) {
            logModuleCall('OTP Verification', 'CheckClient', "Client not found for ID: $clientId", ['clientId' => $clientId]);
            sendResponse('error', 'Không tìm thấy thông tin khách hàng.');
        }
        
        $storedOtp = $client->securityqans;
        $expiryTime = $client->otp_expiry ?? null;
        
        // Check if OTP has expired
        if ($expiryTime && strtotime($expiryTime) < time()) {
            logModuleCall('OTP Verification', 'VerifyOTP', "OTP expired for client ID: $clientId", [
                'clientId' => $clientId,
                'expiryTime' => $expiryTime
            ]);
            
            sendToDiscordWebhook(
                "⏰ OTP Expired",
                "Attempted to use expired OTP",
                ********,
                [
                    ['name' => 'Client ID', 'value' => $clientId, 'inline' => true],
                    ['name' => 'Email', 'value' => $client->email, 'inline' => true],
                    ['name' => 'Expired At', 'value' => $expiryTime, 'inline' => true]
                ]
            );
            
            sendResponse('error', 'Mã OTP đã hết hạn. Vui lòng sử dụng nút "Gửi lại OTP" để nhận mã mới.');
        }
        
        logModuleCall('OTP Verification', 'VerifyOTP', "Input: $otpInput, Stored: $storedOtp", ['clientId' => $clientId]);
        
        if ($otpInput === $storedOtp) {
            // Activate the account
            $updateResult = Capsule::table('tblclients')->where('id', $clientId)->update([
                'status' => 'Active',
                'securityqans' => '',
                'otp_expiry' => null,
                'otp_attempts' => 0
            ]);
            
            if (!$updateResult) {
                throw new Exception("Failed to activate client ID: $clientId");
            }
            
            logModuleCall('OTP Verification', 'ActivateAccount', "Client ID $clientId activated successfully", ['clientId' => $clientId]);
            
            sendToDiscordWebhook(
                "✅ OTP Verification Success",
                "Account has been successfully verified and activated!",
                5763719,
                [
                    ['name' => 'Client ID', 'value' => $clientId, 'inline' => true],
                    ['name' => 'Email', 'value' => $client->email, 'inline' => true]
                ]
            );
            
            $email = $client->email;
            $user = Capsule::table('tblusers')
                ->where('email', $email)
                ->select('id', 'password')
                ->first();
            
            if (!$user || empty($user->password)) {
                logModuleCall('OTP Verification', 'Login', "No user or password found for client ID: $clientId in tblusers", [
                    'clientId' => $clientId,
                    'email' => $email
                ]);
                sendResponse('success', 'Xác minh OTP thành công! Vui lòng đăng nhập thủ công.', 'login.php?email=' . urlencode($email));
            }
            
            $userId = $user->id;
            $password = $user->password;
            
            $_SESSION = array();
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            session_destroy();
            session_start();
            session_regenerate_id(true);
            
            $command = 'CreateSsoToken';
            $adminUsername = 'admin';
            $values = [
                'user_id' => $userId,
                'destination' => 'clientarea:services',
            ];
            
            $results = localAPI($command, $values, $adminUsername);
            
            if ($results['result'] === 'success' && isset($results['redirect_url'])) {
                logModuleCall('OTP Verification', 'Login', "SSO Token created for client ID: $clientId", [
                    'clientId' => $clientId,
                    'userId' => $userId,
                    'email' => $email,
                    'password_hash' => $password,
                    'api_result' => $results,
                    'redirect_url' => $results['redirect_url']
                ]);
                sendResponse('success', 'Xác minh OTP thành công! Đang chuyển hướng đến danh sách dịch vụ...', $results['redirect_url']);
            } else {
                logModuleCall('OTP Verification', 'Login', "Failed to create SSO token for client ID: $clientId", [
                    'clientId' => $clientId,
                    'userId' => $userId,
                    'email' => $email,
                    'api_result' => $results
                ]);
                sendResponse('success', 'Xác minh OTP thành công! Vui lòng đăng nhập thủ công.', 'login.php?email=' . urlencode($email));
            }
        } else {
            logModuleCall('OTP Verification', 'VerifyOTP', "Invalid OTP for client ID: $clientId", [
                'input' => $otpInput,
                'stored' => $storedOtp
            ]);
            
            sendToDiscordWebhook(
                "❌ OTP Verification Failed",
                "Invalid OTP entered for account verification",
                ********,
                [
                    ['name' => 'Client ID', 'value' => $clientId, 'inline' => true],
                    ['name' => 'Email', 'value' => $client->email, 'inline' => true],
                    ['name' => 'Input OTP', 'value' => $otpInput, 'inline' => false]
                ]
            );
            
            // Send error message
            echo json_encode([
                'status' => 'error',
                'message' => 'Mã OTP không đúng! Vui lòng nhập lại.'
            ]);
            exit;
        }
    } catch (Exception $e) {
        logModuleCall('OTP Verification', 'Exception', "Error during OTP verification: " . $e->getMessage(), [
            'clientId' => $clientId,
            'exception' => $e->getTraceAsString()
        ]);
        sendToDiscordWebhook(
            "⚠️ OTP Verification Error",
            "System error occurred during verification",
            ********,
            [
                ['name' => 'Client ID', 'value' => $clientId, 'inline' => true],
                ['name' => 'Error', 'value' => $e->getMessage(), 'inline' => false]
            ]
        );
        sendResponse('error', 'Đã xảy ra lỗi hệ thống. Vui lòng thử lại sau.');
    }
}

logModuleCall('OTP Verification', 'InvalidRequest', "Invalid request method or missing OTP", $_SERVER);
sendToDiscordWebhook(
    "⚠️ Invalid OTP Request",
    "Received an invalid verification request",
    ********,
    [
        ['name' => 'IP Address', 'value' => $_SERVER['REMOTE_ADDR'], 'inline' => true]
    ]
);
sendResponse('error', 'Yêu cầu không hợp lệ.');
?>