<?php
require_once __DIR__ . '/init.php';
use WHMCS\Database\Capsule;

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Yêu cầu không hợp lệ.']);
    exit;
}

$clientId = $_POST['client_id'] ?? ($_SESSION['uid'] ?? null);
if (!$clientId) {
    error_log("Không tìm thấy client_id: POST=" . json_encode($_POST) . ", SESSION=" . json_encode($_SESSION));
    echo json_encode(['status' => 'error', 'message' => 'Phiên đăng nhập không hợp lệ. Vui lòng thử lại.', 'debug' => 'client_id missing']);
    exit;
}

try {
    $client = Capsule::table('tblclients')->where('id', $clientId)->first();
    if (!$client) {
        logModuleCall('OTP Verification', 'ResendOTP', "Client not found for ID: $clientId", ['clientId' => $clientId]);
        echo json_encode(['status' => 'error', 'message' => 'Không tìm thấy thông tin khách hàng.']);
        exit;
    }

    $otpAttempts = isset($client->otp_attempts) ? (int)$client->otp_attempts : 0;
    if ($otpAttempts >= 3) {
        logModuleCall('OTP Verification', 'ResendOTP', "Max attempts reached for client ID: $clientId", ['clientId' => $clientId]);
        echo json_encode(['status' => 'error', 'message' => 'Bạn đã hết lượt gửi lại OTP (tối đa 3 lần).']);
        exit;
    }

    $newOtp = sprintf("%06d", rand(0, 999999));
    $expiryTime = date('Y-m-d H:i:s', strtotime('+5 minutes'));

    $updateData = [
        'securityqans' => $newOtp,
        'otp_expiry' => $expiryTime,
    ];
    if (Capsule::schema()->hasColumn('tblclients', 'otp_attempts')) {
        $updateData['otp_attempts'] = $otpAttempts + 1;
    }

    Capsule::table('tblclients')->where('id', $clientId)->update($updateData);

    logModuleCall('OTP Verification', 'ResendOTP', "New OTP generated for client ID: $clientId", [
        'clientId' => $clientId,
        'expiryTime' => $expiryTime,
        'attempts' => $otpAttempts + 1
    ]);

    // TODO: Thêm logic gửi OTP qua email/Zalo
    // Ví dụ: mail($client->email, "Mã OTP mới", "Mã OTP của bạn là: $newOtp");

    echo json_encode([
        'status' => 'success',
        'message' => 'Mã OTP mới đã được gửi! Vui lòng kiểm tra lại.',
        'attempts_left' => 3 - ($otpAttempts + 1),
        'expiry_time' => $expiryTime
    ]);
} catch (Exception $e) {
    logModuleCall('OTP Verification', 'Exception', "Error: " . $e->getMessage(), ['clientId' => $clientId]);
    echo json_encode(['status' => 'error', 'message' => 'Lỗi khi gửi lại OTP: ' . $e->getMessage()]);
}
exit;