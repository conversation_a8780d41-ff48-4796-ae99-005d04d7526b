<?php
define("CLIENTAREA", true);
require "init.php";
require "includes/clientfunctions.php";
require "includes/customfieldfunctions.php";
use WHMCS\Database\Capsule;

if (isset($_SESSION["uid"])) {
    redir("", "clientarea.php");
}

$captcha = new WHMCS\Utility\Captcha();
$securityquestions = getSecurityQuestions();

$firstname = $whmcs->get_req_var("firstname");
$lastname = $whmcs->get_req_var("lastname");
$companyname = $whmcs->get_req_var("companyname");
$email = $whmcs->get_req_var("email");
$address1 = $whmcs->get_req_var("address1");
$address2 = $whmcs->get_req_var("address2");
$city = $whmcs->get_req_var("city");
$state = $whmcs->get_req_var("state");
$postcode = $whmcs->get_req_var("postcode");
$country = $whmcs->get_req_var("country");
$phonenumber = $whmcs->get_req_var("phonenumber");
$password = $whmcs->get_req_var("password");
$securityqid = $whmcs->get_req_var("securityqid");
$securityqans = $whmcs->get_req_var("securityqans");
$customfield = $whmcs->get_req_var("customfield");
$marketingoptin = $whmcs->get_req_var("marketingoptin");
$taxId = App::getFromRequest(WHMCS\Billing\Tax\Vat::getFieldName());

$webhookUrl = "https://discord.com/api/webhooks/1347111685747376128/pCeFakAoNwl8HvvDDUET2ATwHgtH3M15nKz-9vh8DPGNKdATEmT-0DIiJk4ddxD83ooR";

$errormessage = "";
if ($whmcs->get_req_var("register")) {
    check_token();
    $errormessage = checkDetailsareValid("", true);
    if (!$errormessage) {
        $phonenumber = App::formatPostedPhoneNumber();

        $command = 'AddClient';
        $postData = [
            'firstname' => $firstname,
            'lastname' => $lastname,
            'companyname' => $companyname,
            'email' => $email,
            'address1' => $address1,
            'address2' => $address2,
            'city' => $city,
            'state' => $state,
            'postcode' => $postcode,
            'country' => $country,
            'phonenumber' => $phonenumber,
            'password2' => $password,
            'securityqid' => $securityqid,
            'securityqans' => '',
            'tax_id' => $taxId,
            'marketingoptin' => (bool) $marketingoptin,
            'language' => 'vietnamese',
            'clientip' => $_SERVER['REMOTE_ADDR'],
            'noemail' => true,
        ];
        $adminUsername = 'admin';

        $results = localAPI($command, $postData, $adminUsername);

        if ($results['result'] === 'success' && isset($results['clientid'])) {
            $userid = $results['clientid'];
            logActivity("Client created successfully via API: ID $userid, Email: $email, Phone: $phonenumber");

            $embedData = [
                "title" => "Khách hàng mới đăng ký",
                "color" => 5763719,
                "fields" => [
                    ["name" => "ID", "value" => (string)$userid, "inline" => true],
                    ["name" => "Email", "value" => $email, "inline" => true],
                    ["name" => "Số điện thoại", "value" => $phonenumber, "inline" => true],
                    ["name" => "Thời gian", "value" => date('Y-m-d H:i:s'), "inline" => false]
                ],
                "timestamp" => date('c')
            ];
            sendDiscordWebhook($webhookUrl, $embedData);

            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }

            $otp = rand(100000, 999999);
            $otpSendSuccess = sendZnsAndEmailOtp($phonenumber, $otp, $email, $userid, $webhookUrl);

            if ($otpSendSuccess) {
                $updateResult = Capsule::table('tblclients')->where('id', $userid)->update([
                    'securityqans' => $otp,
                    'status' => 'Inactive'
                ]);
                if ($updateResult) {
                    logActivity("OTP updated for client ID: $userid, OTP: $otp");
                    $_SESSION['otp_verification'] = true;
                    $_SESSION['client_id'] = $userid;
                } else {
                    $errorMsg = "Failed to update OTP for client ID: $userid";
                    logActivity($errorMsg);
                    sendDiscordError($webhookUrl, "OTP Update Error", $errorMsg, $email, $phonenumber);
                    header("Location: register.php?error=otp_update_failed");
                    exit;
                }
            } else {
                $errorMsg = "Failed to send OTP for client ID: $userid via both ZNS and Email";
                logActivity($errorMsg);
                sendDiscordError($webhookUrl, "OTP Send Error", $errorMsg, $email, $phonenumber);
                header("Location: register.php?error=otp_send_failed");
                exit;
            }

            redir("", "otp_verification.php");
        } else {
            $errorMsg = "Failed to create client via API: " . json_encode($results);
            logActivity($errorMsg);
            sendDiscordError($webhookUrl, "Client Creation Error", $errorMsg, $email, $phonenumber);
            header("Location: register.php?error=save_failed");
            exit;
        }
    } else {
        sendDiscordError($webhookUrl, "Validation Error", $errormessage, $email, $phonenumber);
    }
}

$pagetitle = Lang::trans("clientregistertitle");
$breadcrumbnav = "<a href=\"index.php\">" . Lang::trans("globalsystemname") . "</a> > <a href=\"register.php\">" . Lang::trans("clientregistertitle") . "</a>";
$pageicon = "images/order_big.gif";
$displayTitle = Lang::trans("clientregistertitle");
$tagline = Lang::trans("registerintro");

initialiseClientArea($pagetitle, $displayTitle, $tagline, $pageicon, $breadcrumbnav);
$templatefile = "clientregister";

$smarty->assign("registrationDisabled", (bool) (!WHMCS\Config\Setting::getValue("AllowClientRegister")));
$smarty->assign("noregistration", !WHMCS\Config\Setting::getValue("AllowClientRegister") ? true : false);
$countries = new WHMCS\Utility\Country();
$countriesdropdown = getCountriesDropDown($country);
$smarty->assign("defaultCountry", WHMCS\Config\Setting::getValue("DefaultCountry"));
$smarty->assign("errormessage", $errormessage);
$smarty->assign("clientfirstname", $firstname);
$smarty->assign("clientlastname", $lastname);
$smarty->assign("clientcompanyname", $companyname);
$smarty->assign("clientemail", $email);
$smarty->assign("clientaddress1", $address1);
$smarty->assign("clientaddress2", $address2);
$smarty->assign("clientcity", $city);
$smarty->assign("clientstate", $state);
$smarty->assign("clientpostcode", $postcode);
$smarty->assign("clientcountry", $country);
$smarty->assign("clientcountriesdropdown", $countriesdropdown);
$smarty->assign("clientcountries", $countries->getCountryNameArray());
$smarty->assign("clientphonenumber", $phonenumber);
$smarty->assign("securityquestions", $securityquestions);
$customfields = getCustomFields("client", "", "", "", "on", $customfield);
$smarty->assign("customfields", $customfields);
$smarty->assign("captcha", $captcha);
$smarty->assign("captchaForm", WHMCS\Utility\Captcha::FORM_REGISTRATION);
$smarty->assign("recaptchahtml", clientAreaReCaptchaHTML());
$smarty->assign("accepttos", WHMCS\Config\Setting::getValue("EnableTOSAccept"));
$smarty->assign("tosurl", WHMCS\Config\Setting::getValue("TermsOfService"));
$smarty->assign("uneditablefields", explode(",", WHMCS\Config\Setting::getValue("ClientsProfileUneditableFields")));
$optionalFields = $whmcs->get_config("ClientsProfileOptionalFields");
$smarty->assign("optionalFields", explode(",", $optionalFields));
$smarty->assign("phoneNumberInputStyle", (int) WHMCS\Config\Setting::getValue("PhoneNumberDropdown"));
$smarty->assign("showMarketingEmailOptIn", WHMCS\Config\Setting::getValue("AllowClientsEmailOptOut"));
$smarty->assign("marketingEmailOptInMessage", Lang::trans("emailMarketing.optInMessage") != "emailMarketing.optInMessage" ? Lang::trans("emailMarketing.optInMessage") : WHMCS\Config\Setting::getValue("EmailMarketingOptInMessage"));
$smarty->assign("marketingEmailOptIn", App::isInRequest("marketingoptin") ? (bool) App::getFromRequest("marketingoptin") : (bool) (!WHMCS\Config\Setting::getValue("EmailMarketingRequireOptIn")));

Menu::addContext("securityQuestions", $securityquestions);
Menu::primarySidebar("clientRegistration");
outputClientArea($templatefile, false, array("ClientAreaPageRegister"));

function sendDiscordWebhook($webhookUrl, $embedData) {
    $data = [
        "username" => "Registration Bot",
        "avatar_url" => "https://i.imgur.com/4M34hi2.png",
        "embeds" => [$embedData]
    ];

    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 204) {
        logActivity("Discord webhook (embed) sent successfully: " . json_encode($embedData));
        return true;
    } else {
        logActivity("Failed to send Discord webhook (embed): HTTP $httpCode - $response");
        return false;
    }
}

function sendDiscordError($webhookUrl, $title, $errorMsg, $email, $phonenumber) {
    $embedData = [
        "title" => $title,
        "color" => 15548997, // Màu đỏ
        "fields" => [
            ["name" => "Error Message", "value" => $errorMsg, "inline" => false],
            ["name" => "Email", "value" => $email ?: "N/A", "inline" => true],
            ["name" => "Số điện thoại", "value" => $phonenumber ?: "N/A", "inline" => true],
            ["name" => "Thời gian", "value" => date('Y-m-d H:i:s'), "inline" => false]
        ],
        "timestamp" => date('c')
    ];

    $data = [
        "username" => "Registration Bot",
        "avatar_url" => "https://i.imgur.com/4M34hi2.png",
        "embeds" => [$embedData]
    ];

    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_exec($ch);
    curl_close($ch);
}

function sendDiscordSuccess($webhookUrl, $title, $clientId, $email, $phone, $dailyQuota = 'N/A', $remainingQuota = 'N/A') {
    $embedData = [
        "title" => $title,
        "color" => 5763719, // Màu xanh lá
        "fields" => [
            ["name" => "ID", "value" => (string)$clientId, "inline" => true],
            ["name" => "Email", "value" => $email ?: "N/A", "inline" => true],
            ["name" => "Số điện thoại", "value" => $phone ?: "N/A", "inline" => true],
            ["name" => "Daily Quota", "value" => (string)$dailyQuota, "inline" => true],
            ["name" => "Remaining Quota", "value" => (string)$remainingQuota, "inline" => true],
            ["name" => "Thời gian", "value" => date('Y-m-d H:i:s'), "inline" => false]
        ],
        "timestamp" => date('c')
    ];

    $data = [
        "username" => "Registration Bot",
        "avatar_url" => "https://i.imgur.com/4M34hi2.png",
        "embeds" => [$embedData]
    ];

    $ch = curl_init($webhookUrl);
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 204) {
        logActivity("Discord success webhook sent: " . json_encode($embedData));
        return true;
    } else {
        logActivity("Failed to send Discord success webhook: HTTP $httpCode - $response");
        return false;
    }
}

function sendZnsAndEmailOtp($phone, $otp, $email, $clientId, $webhookUrl) {
    $znsResult = sendZnsOtp($phone, $otp, $email, $clientId, $webhookUrl);
    $emailResult = sendOtpViaEmail($phone, $otp, $email, $clientId, $webhookUrl);

    // Trả về true nếu ít nhất một phương thức thành công
    return $znsResult || $emailResult;
}

function sendZnsOtp($phone, $otp, $email, $clientId, $webhookUrl) {
    $today = date('Y-m-d');
    $znsCount = Capsule::table('tblotp_logs')
        ->where('phonenumber', $phone)
        ->where('method', 'zns')
        ->where('sent_at', '>=', $today . ' 00:00:00')
        ->where('sent_at', '<=', $today . ' 23:59:59')
        ->count();

    logActivity("Checking ZNS count for $phone on $today: $znsCount");

    if ($znsCount >= 3) {
        logActivity("ZNS limit reached for $phone on $today");
        sendDiscordError($webhookUrl, "ZNS Limit Reached", "Daily ZNS limit (3) reached for $phone", $email, $phone);
        return false;
    }

    $tokenData = Capsule::table('tblzalo_tokens')->first();
    if (!$tokenData || empty($tokenData->access_token)) {
        $errorMsg = "Không tìm thấy access_token trong tblzalo_tokens";
        logModuleCall('ZaloZNS', 'SendOTP', [], $errorMsg);
        sendDiscordError($webhookUrl, "ZNS Token Error", $errorMsg, $email, $phone);
        return false;
    }

    $accessToken = $tokenData->access_token;
    $templateId = "412474";
    $url = "https://business.openapi.zalo.me/message/template";

    $phone = str_replace(" ", "", $phone);
    $phone = str_replace(".", "", $phone);
    $phone = ltrim($phone, '+');
    $phone = preg_replace('/^84/', '84', $phone);

    if (!preg_match('/^84\d{9}$/', $phone)) {
        $errorMsg = "Số điện thoại không đúng định dạng (***********): $phone";
        logModuleCall('ZaloZNS', 'SendOTP', ['phone' => $phone], $errorMsg);
        sendDiscordError($webhookUrl, "ZNS Phone Format Error", $errorMsg, $email, $phone);
        return false;
    }

    $requestData = [
        "phone" => $phone,
        "template_id" => $templateId,
        "template_data" => [
            "otp" => (string)$otp
        ],
        "tracking_id" => "otp_" . time()
    ];

    logModuleCall('ZaloZNS', 'SendOTP', $requestData, "Sending OTP to $phone: $otp");

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json",
        "access_token: $accessToken"
    ]);
    $response = curl_exec($ch);
    $error = curl_error($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($error || $httpCode !== 200) {
        $errorMsg = "CURL Error: $error, HTTP Code: $httpCode";
        logModuleCall('ZaloZNS', 'SendOTP', $requestData, $errorMsg);
        sendDiscordError($webhookUrl, "ZNS CURL Error", $errorMsg, $email, $phone);
        return false;
    }

    $responseData = json_decode($response, true);
    if (!$responseData || !isset($responseData['error']) || $responseData['error'] !== 0) {
        $errorCode = $responseData['error'] ?? 'Unknown';
        $errorMsg = "API Error: Code $errorCode - " . ($responseData['message'] ?? 'No message provided');
        logModuleCall('ZaloZNS', 'SendOTP', $requestData, $errorMsg);
        sendDiscordError($webhookUrl, "ZNS API Error", $errorMsg, $email, $phone);
        return false;
    }

    Capsule::table('tblotp_logs')->insert([
        'phonenumber' => $phone,
        'otp' => $otp,
        'method' => 'zns',
        'sent_at' => date('Y-m-d H:i:s')
    ]);

    $dailyQuota = $responseData['data']['quota']['dailyQuota'] ?? 'N/A';
    $remainingQuota = $responseData['data']['quota']['remainingQuota'] ?? 'N/A';
    sendDiscordSuccess($webhookUrl, "Gửi OTP ZNS Thành Công", $clientId, $email, $phone, $dailyQuota, $remainingQuota);

    logModuleCall('ZaloZNS', 'SendOTP', $requestData, "Success: Message ID " . $responseData['data']['msg_id']);
    return true;
}

function sendOtpViaEmail($phone, $otp, $email, $clientId, $webhookUrl) {
    $command = 'SendEmail';
    $postData = [
        'messagename' => 'Client Signup Email',
        'id' => $clientId,
        'customvars' => base64_encode(serialize(['otp' => $otp])),
    ];
    $adminUsername = 'admin';

    $results = localAPI($command, $postData, $adminUsername);

    if ($results['result'] === 'success') {
        Capsule::table('tblotp_logs')->insert([
            'phonenumber' => $phone,
            'otp' => $otp,
            'method' => 'email',
            'sent_at' => date('Y-m-d H:i:s')
        ]);
        logActivity("OTP sent via email to $email: $otp");
        sendDiscordSuccess($webhookUrl, "Gửi OTP Email Thành Công", $clientId, $email, $phone);
        return true;
    } else {
        $errorMsg = "Failed to send OTP via email to $email: " . json_encode($results);
        logActivity($errorMsg);
        sendDiscordError($webhookUrl, "Email OTP Error", $errorMsg, $email, $phone);
        return false;
    }
}
?>