<?php
/**
 * <PERSON><PERSON><PERSON> tra cập nhật rule
 */

// <PERSON><PERSON>t nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON><PERSON> cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

echo "<!DOCTYPE html>
<html>
<head>
    <title>Kiểm tra cập nhật rule</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeeba; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-secondary { background: #6c757d; }
        input, select { padding: 8px; width: 100%; box-sizing: border-box; }
        .form-group { margin-bottom: 15px; }
        h1, h2 { color: #333; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
    </style>
</head>
<body>
    <div class='container'>";

// Ghi log
function writeLog($message) {
    echo "<div class='alert alert-info'>" . $message . "</div>";
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [UPDATE CHECK] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Xử lý form cập nhật
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    try {
        $ruleId = (int)$_POST['rule_id'];
        $minAmount = (float)$_POST['min_amount'];
        $maxAmount = (float)$_POST['max_amount'];
        $bonusPercent = (float)$_POST['bonus_percent'];
        $description = $_POST['description'];
        $active = isset($_POST['active']) ? 1 : 0;
        
        // Lấy rule hiện tại để debug
        $beforeUpdate = Capsule::table('mod_addfundbonus_rules')
            ->where('id', $ruleId)
            ->first();
            
        // Hiển thị dữ liệu trước khi cập nhật
        echo "<h2>Dữ liệu trước khi cập nhật:</h2>";
        echo "<pre>" . print_r($beforeUpdate, true) . "</pre>";
        
        // Hiển thị dữ liệu sẽ cập nhật
        echo "<h2>Dữ liệu chuẩn bị cập nhật:</h2>";
        echo "<pre>
ID: $ruleId
Min Amount: $minAmount
Max Amount: $maxAmount
Bonus Percent: $bonusPercent
Description: $description
Active: $active
        </pre>";
        
        // Cập nhật rule
        $updated = Capsule::table('mod_addfundbonus_rules')
            ->where('id', $ruleId)
            ->update([
                'min_amount' => $minAmount,
                'max_amount' => $maxAmount,
                'bonus_percent' => $bonusPercent,
                'description' => $description,
                'active' => $active,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
        // Lấy rule sau khi cập nhật
        $afterUpdate = Capsule::table('mod_addfundbonus_rules')
            ->where('id', $ruleId)
            ->first();
            
        // Hiển thị dữ liệu sau khi cập nhật
        echo "<h2>Dữ liệu sau khi cập nhật:</h2>";
        echo "<pre>" . print_r($afterUpdate, true) . "</pre>";
        
        if ($updated) {
            writeLog("Đã cập nhật rule ID: $ruleId thành công. Rows affected: $updated");
            echo "<div class='alert alert-success'>Đã cập nhật rule thành công!</div>";
        } else {
            writeLog("Không có gì thay đổi hoặc lỗi khi cập nhật rule ID: $ruleId");
            echo "<div class='alert alert-warning'>Không có gì thay đổi hoặc ID không tồn tại!</div>";
        }
    } catch (Exception $e) {
        writeLog("Lỗi cập nhật rule: " . $e->getMessage());
        echo "<div class='alert alert-danger'>Lỗi: " . $e->getMessage() . "</div>";
    }
}

// Kiểm tra bảng cấu trúc của bảng
try {
    $tableInfo = Capsule::connection()->select("DESCRIBE mod_addfundbonus_rules");
    echo "<h2>Cấu trúc bảng mod_addfundbonus_rules:</h2>";
    echo "<table>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($tableInfo as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . ($value === null ? 'NULL' : $value) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    writeLog("Đã kiểm tra cấu trúc bảng mod_addfundbonus_rules");
} catch (Exception $e) {
    writeLog("Lỗi khi kiểm tra cấu trúc bảng: " . $e->getMessage());
    echo "<div class='alert alert-danger'>Lỗi khi kiểm tra cấu trúc bảng: " . $e->getMessage() . "</div>";
}

// Hiển thị danh sách rules và form cập nhật
try {
    // Lấy danh sách rules
    $rules = Capsule::table('mod_addfundbonus_rules')->get();
    $ruleCount = count($rules);
    
    writeLog("Đã lấy $ruleCount rules từ database");
    
    echo "<h1>Kiểm tra cập nhật Rule AddFundBonus</h1>";
    echo "<div class='alert alert-info'>Tổng số rules: $ruleCount</div>";
    
    if ($ruleCount > 0) {
        echo "<h2>Danh sách Rules hiện tại:</h2>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Min</th><th>Max</th><th>Bonus %</th><th>Mô tả</th><th>Trạng thái</th><th>Thao tác</th></tr>";
        
        foreach ($rules as $rule) {
            echo "<tr>";
            echo "<td>" . $rule->id . "</td>";
            echo "<td>" . $rule->min_amount . "</td>";
            echo "<td>" . ($rule->max_amount > 0 ? $rule->max_amount : 'Không giới hạn') . "</td>";
            echo "<td>" . $rule->bonus_percent . "%</td>";
            echo "<td>" . $rule->description . "</td>";
            echo "<td>" . ($rule->active ? 'Kích hoạt' : 'Tắt') . "</td>";
            echo "<td><a href='?edit=" . $rule->id . "' class='btn'>Sửa</a></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Hiển thị form cập nhật nếu có id
        if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
            $ruleId = (int)$_GET['edit'];
            $ruleToEdit = Capsule::table('mod_addfundbonus_rules')
                ->where('id', $ruleId)
                ->first();
                
            if ($ruleToEdit) {
                echo "<h2>Cập nhật Rule ID: " . $ruleToEdit->id . "</h2>";
                echo "<form method='post' action=''>";
                echo "<input type='hidden' name='action' value='update'>";
                echo "<input type='hidden' name='rule_id' value='" . $ruleToEdit->id . "'>";
                
                echo "<div class='form-group'>";
                echo "<label>Số tiền tối thiểu:</label>";
                echo "<input type='number' name='min_amount' step='0.01' value='" . $ruleToEdit->min_amount . "' required>";
                echo "</div>";
                
                echo "<div class='form-group'>";
                echo "<label>Số tiền tối đa (0 = không giới hạn):</label>";
                echo "<input type='number' name='max_amount' step='0.01' value='" . $ruleToEdit->max_amount . "'>";
                echo "</div>";
                
                echo "<div class='form-group'>";
                echo "<label>Phần trăm thưởng (%):</label>";
                echo "<input type='number' name='bonus_percent' step='0.01' value='" . $ruleToEdit->bonus_percent . "' required>";
                echo "</div>";
                
                echo "<div class='form-group'>";
                echo "<label>Mô tả:</label>";
                echo "<input type='text' name='description' value='" . htmlspecialchars($ruleToEdit->description) . "' required>";
                echo "</div>";
                
                echo "<div class='form-group'>";
                echo "<label><input type='checkbox' name='active' " . ($ruleToEdit->active ? 'checked' : '') . "> Kích hoạt</label>";
                echo "</div>";
                
                echo "<div class='form-group'>";
                echo "<button type='submit' class='btn'>Cập nhật</button>";
                echo "<a href='" . $_SERVER['PHP_SELF'] . "' class='btn btn-secondary' style='margin-left: 10px;'>Hủy</a>";
                echo "</div>";
                
                echo "</form>";
                
                writeLog("Đang sửa rule ID: " . $ruleToEdit->id);
            } else {
                echo "<div class='alert alert-danger'>Không tìm thấy rule với ID: $ruleId</div>";
            }
        }
    } else {
        echo "<div class='alert alert-warning'>Không có rule nào trong hệ thống.</div>";
        echo "<p>Thêm rule mẫu bằng cách <a href='create_samples.php' class='btn'>Thêm 3 Rules mẫu</a></p>";
    }
    
    // Hiển thị SQL debug cho module
    try {
        $moduleFile = ROOTDIR . '/modules/addons/addfundbonus/addfundbonus.php';
        if (file_exists($moduleFile)) {
            $moduleContent = file_get_contents($moduleFile);
            
            // Tìm các đoạn SQL update
            if (preg_match_all('/update\s*\(\s*(.+?)\s*\)/is', $moduleContent, $matches)) {
                echo "<h2>SQL Update trong module:</h2>";
                echo "<pre>";
                print_r($matches[0]);
                echo "</pre>";
            }
        }
    } catch (Exception $e) {
        writeLog("Lỗi khi đọc module file: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    writeLog("Lỗi: " . $e->getMessage());
    echo "<div class='alert alert-danger'>Lỗi: " . $e->getMessage() . "</div>";
}

echo "
        <div style='margin-top: 20px;'>
            <a href='simplified_test.php' class='btn'>Quay lại trang kiểm tra</a>
            <a href='addonmodules.php?module=addfundbonus' class='btn'>Đi đến module</a>
        </div>
    </div>
</body>
</html>";
?> 