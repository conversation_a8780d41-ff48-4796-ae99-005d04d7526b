<?php

use Monolog\Formatter\LineFormatter;
use Monolog\Handler\ErrorLogHandler;
use Monolog\Logger;

/**
 * dist.loghandler.php
 *
 ****************************
 ** DO NOT EDIT THIS FILE! **
 ****************************
 *
 * You are free to copy this file as "loghandler.php" and make any
 * modification you need.  This allows you to make customization that will not
 * be overwritten during an update.
 *
 * WHMCS will attempt to load your custom "loghandler.php" instead of this
 * file ("dist.loghandler.php").
 *
 ****************************
 ** DO NOT EDIT THIS FILE! **
 ****************************
 *
 * The WHMCS initializes a Monolog logger, exposing the handler for customization.
 *
 * You are free to customize the handlers by modify this file to your needs.
 *
 * By default, WHMCS will log all messages to the configured PHP error log
 * (i.e., the Apache webserver error log).
 *
 * NOTE:
 * * The applications handler by default, as defined here, will log at the
 *   'warning' level, if most verbose is required, consider 'info' or 'debug'
 *
 * Please see Monolog documentation for usage of handlers and log levels
 * @link https://github.com/Seldaek/monolog
 */

if (!defined("ROOTDIR")) {
    die("This file cannot be accessed directly");
}

$handle = new ErrorLogHandler(
    ErrorLogHandler::OPERATING_SYSTEM,
    Logger::WARNING
);

$handle->setFormatter(
    new LineFormatter(
        '[%channel%] %level_name%: %message% %context% %extra%'
    )
);

Log::pushHandler($handle);
