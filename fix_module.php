<?php
/**
 * <PERSON><PERSON>ng cụ sửa lỗi module AddFundBonus
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON><PERSON> cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

// Ghi log
function writeLog($message) {
    echo $message . "<br>";
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [MODULE FIX] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>Sửa lỗi module AddFundBonus</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeeba; }
        .btn { display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Công cụ sửa lỗi module AddFundBonus</h1>";

// Kiểm tra module
$moduleFile = ROOTDIR . '/modules/addons/addfundbonus/addfundbonus.php';
$moduleExists = file_exists($moduleFile);

if (!$moduleExists) {
    echo "<div class='alert alert-danger'>Module file không tồn tại tại đường dẫn: $moduleFile</div>";
    die("</div></body></html>");
}

// Kiểm tra và tạo thư mục logs nếu chưa có
$logDir = ROOTDIR . '/modules/addons/addfundbonus/logs';
if (!is_dir($logDir)) {
    if (mkdir($logDir, 0755, true)) {
        writeLog("Đã tạo thư mục logs: $logDir");
    } else {
        echo "<div class='alert alert-danger'>Không thể tạo thư mục logs: $logDir</div>";
    }
}

// Kiểm tra và thêm trường thời gian hiệu lực cho bảng rules
try {
    $hasStartDateField = false;
    $hasEndDateField = false;
    
    // Kiểm tra các trường đã tồn tại chưa
    $columns = Capsule::schema()->getColumnListing('mod_addfundbonus_rules');
    $hasStartDateField = in_array('start_date', $columns);
    $hasEndDateField = in_array('end_date', $columns);
    
    if (!$hasStartDateField) {
        Capsule::schema()->table('mod_addfundbonus_rules', function ($table) {
            $table->date('start_date')->nullable()->after('active');
        });
        writeLog("Đã thêm trường start_date vào bảng mod_addfundbonus_rules");
    }
    
    if (!$hasEndDateField) {
        Capsule::schema()->table('mod_addfundbonus_rules', function ($table) {
            $table->date('end_date')->nullable()->after('start_date');
        });
        writeLog("Đã thêm trường end_date vào bảng mod_addfundbonus_rules");
    }
} catch (Exception $e) {
    writeLog("Lỗi khi cập nhật cấu trúc bảng: " . $e->getMessage());
    echo "<div class='alert alert-danger'>Lỗi khi cập nhật cấu trúc bảng: " . $e->getMessage() . "</div>";
}

// Sửa lỗi bắt đầu
writeLog("Bắt đầu kiểm tra và sửa lỗi module...");

// Kiểm tra bảng rules
try {
    $rulesExists = Capsule::schema()->hasTable('mod_addfundbonus_rules');
    
    if ($rulesExists) {
        writeLog("Bảng rules đã tồn tại");
        
        // Đếm số rules
        $ruleCount = Capsule::table('mod_addfundbonus_rules')->count();
        writeLog("Hiện có $ruleCount rules trong hệ thống");
        
        // Nếu không có rule nào, thêm rules mẫu
        if ($ruleCount === 0) {
            writeLog("Không có rule nào, đang thêm rules mẫu...");
            
            // Rule 1
            Capsule::table('mod_addfundbonus_rules')->insert([
                'min_amount' => 100.00,
                'max_amount' => 500.00,
                'bonus_percent' => 5.00,
                'description' => 'Khuyến mãi 5% cho nạp từ 100-500',
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            writeLog("Đã thêm rule 1: Khuyến mãi 5% cho nạp từ 100-500");
            
            // Rule 2
            Capsule::table('mod_addfundbonus_rules')->insert([
                'min_amount' => 500.01,
                'max_amount' => 1000.00,
                'bonus_percent' => 10.00,
                'description' => 'Khuyến mãi 10% cho nạp từ 500-1000',
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            writeLog("Đã thêm rule 2: Khuyến mãi 10% cho nạp từ 500-1000");
            
            // Rule 3
            Capsule::table('mod_addfundbonus_rules')->insert([
                'min_amount' => 1000.01,
                'max_amount' => 0.00,
                'bonus_percent' => 15.00,
                'description' => 'Khuyến mãi 15% cho nạp trên 1000',
                'active' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            writeLog("Đã thêm rule 3: Khuyến mãi 15% cho nạp trên 1000");
        }
    } else {
        // Tạo bảng rules
        Capsule::schema()->create('mod_addfundbonus_rules', function ($table) {
            $table->increments('id');
            $table->decimal('min_amount', 10, 2)->default(0.00);
            $table->decimal('max_amount', 10, 2)->default(0.00);
            $table->decimal('bonus_percent', 5, 2)->default(0.00);
            $table->string('description', 255);
            $table->boolean('active')->default(1);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->useCurrent();
        });
        writeLog("Đã tạo bảng mod_addfundbonus_rules");
    }
    
    // Kiểm tra logs
    $logsExists = Capsule::schema()->hasTable('mod_addfundbonus_logs');
    
    if ($logsExists) {
        writeLog("Bảng logs đã tồn tại");
    } else {
        // Tạo bảng logs
        Capsule::schema()->create('mod_addfundbonus_logs', function ($table) {
            $table->increments('id');
            $table->integer('client_id');
            $table->integer('transaction_id');
            $table->decimal('amount', 10, 2)->default(0.00);
            $table->decimal('bonus_percent', 5, 2)->default(0.00);
            $table->decimal('bonus_amount', 10, 2)->default(0.00);
            $table->integer('rule_id')->nullable();
            $table->timestamp('created_at')->useCurrent();
        });
        writeLog("Đã tạo bảng mod_addfundbonus_logs");
    }
    
    // Kiểm tra và sửa file module
    $moduleContent = file_get_contents($moduleFile);
    $originalContent = $moduleContent;
    $moduleFixed = false;
    
    // Tìm và sửa lỗi cập nhật rule
    // Kiểm tra xem có lỗi trong SQL không
    $updateRuleProblem = stripos($moduleContent, 'update(') !== false;
    
    if ($updateRuleProblem) {
        writeLog("Phát hiện vấn đề với hàm update() trong module");
        
        // Thay thế các đoạn update() trong file
        $pattern = '/(\$capsule->table\(\'mod_addfundbonus_rules\'\)->where\(\'id\', \$ruleId\)->update)\s*\(\s*\)/is';
        $replacement = '$1([
            \'min_amount\' => $minAmount,
            \'max_amount\' => $maxAmount,
            \'bonus_percent\' => $bonusPercent,
            \'description\' => $description,
            \'active\' => $active,
            \'updated_at\' => date(\'Y-m-d H:i:s\')
        ])';
        
        $newContent = preg_replace($pattern, $replacement, $moduleContent);
        
        // Kiểm tra xem có thay thế nào được thực hiện không
        if ($newContent !== $moduleContent) {
            // Tạo bản sao lưu trước khi cập nhật
            $backupFile = $moduleFile . '.bak.' . time();
            file_put_contents($backupFile, $moduleContent);
            writeLog("Đã tạo bản sao lưu tại: $backupFile");
            
            // Cập nhật file
            file_put_contents($moduleFile, $newContent);
            writeLog("Đã sửa lỗi hàm update() trong module");
            $moduleFixed = true;
        } else {
            // Thử cách khác nếu không khớp mẫu đầu tiên
            $pattern2 = '/(\$result\s*=\s*\$capsule->table\(\'mod_addfundbonus_rules\'\)->where\(\'id\',\s*\$ruleId\)->update)\s*\(\s*\)/is';
            $replacement2 = '$1([
                \'min_amount\' => $minAmount,
                \'max_amount\' => $maxAmount,
                \'bonus_percent\' => $bonusPercent,
                \'description\' => $description,
                \'active\' => $active,
                \'updated_at\' => date(\'Y-m-d H:i:s\')
            ])';
            
            $newContent = preg_replace($pattern2, $replacement2, $moduleContent);
            
            if ($newContent !== $moduleContent) {
                // Tạo bản sao lưu trước khi cập nhật
                $backupFile = $moduleFile . '.bak.' . time();
                file_put_contents($backupFile, $moduleContent);
                writeLog("Đã tạo bản sao lưu tại: $backupFile");
                
                // Cập nhật file
                file_put_contents($moduleFile, $newContent);
                writeLog("Đã sửa lỗi hàm update() trong module (phương pháp 2)");
                $moduleFixed = true;
            } else {
                // Nếu không thể sửa tự động, hiển thị mã cần sửa
                writeLog("Không thể sửa tự động lỗi update. Hiển thị hướng dẫn sửa thủ công.");
                
                // Tìm các đoạn update
                preg_match_all('/.*update\s*\(\s*\).*\n/i', $moduleContent, $matches);
                
                echo "<div class='alert alert-warning'>Không thể sửa tự động. Hãy kiểm tra file module tại $moduleFile và tìm các đoạn code sau để sửa:</div>";
                echo "<pre>";
                print_r($matches[0]);
                echo "</pre>";
                
                echo "<div class='alert alert-info'>Thay thế update() bằng:</div>";
                echo "<pre>update([
    'min_amount' => \$minAmount,
    'max_amount' => \$maxAmount,
    'bonus_percent' => \$bonusPercent,
    'description' => \$description,
    'active' => \$active, 
    'updated_at' => date('Y-m-d H:i:s')
])</pre>";
            }
        }
    } else {
        writeLog("Không tìm thấy vấn đề với hàm update() trong module");
    }
    
    if ($moduleFixed) {
        echo "<div class='alert alert-success'>Đã sửa lỗi module thành công!</div>";
    }
    
    // Tìm xem hàm nào đang được sử dụng để cập nhật rule
    if (preg_match_all('/function\s+(\w+)\s*\([^)]*\)\s*{[^}]*update\s*\(/is', $moduleContent, $matches)) {
        echo "<div class='alert alert-info'>Các hàm xử lý cập nhật rule:</div>";
        echo "<pre>";
        print_r($matches[1]);
        echo "</pre>";
    }
    
    // Kiểm tra cấu trúc bảng
    $tableInfo = Capsule::connection()->select("DESCRIBE mod_addfundbonus_rules");
    echo "<h2>Cấu trúc bảng mod_addfundbonus_rules:</h2>";
    echo "<pre>";
    print_r($tableInfo);
    echo "</pre>";
    
    // Hiển thị module settings
    $moduleSettings = Capsule::table('tbladdonmodules')
        ->where('module', 'addfundbonus')
        ->get();
    
    if (count($moduleSettings) > 0) {
        echo "<h2>Cài đặt module:</h2>";
        echo "<pre>";
        print_r($moduleSettings);
        echo "</pre>";
    } else {
        echo "<div class='alert alert-warning'>Module chưa được kích hoạt!</div>";
    }
    
    // Tạo tool trực tiếp để cập nhật rule
    echo "<h2>Cập nhật trực tiếp rule:</h2>";
    echo "<form method='post' action='direct_update.php'>";
    echo "<input type='submit' value='Tạo công cụ cập nhật trực tiếp' class='btn'>";
    echo "</form>";
    
    // Tạo file direct_update.php nếu chưa tồn tại
    $directUpdateFile = 'direct_update.php';
    if (!file_exists($directUpdateFile)) {
        $directUpdateContent = '<?php
/**
 * Công cụ cập nhật rule trực tiếp
 */

// Kết nối WHMCS
require_once \'init.php\';

// Kiểm tra admin
if (!isset($_SESSION[\'adminid\'])) {
    die("Yêu cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

// Xử lý cập nhật
if ($_SERVER[\'REQUEST_METHOD\'] === \'POST\' && isset($_POST[\'update_rule\'])) {
    try {
        $ruleId = (int)$_POST[\'rule_id\'];
        $minAmount = (float)$_POST[\'min_amount\'];
        $maxAmount = (float)$_POST[\'max_amount\'];
        $bonusPercent = (float)$_POST[\'bonus_percent\'];
        $description = $_POST[\'description\'];
        $active = isset($_POST[\'active\']) ? 1 : 0;
        
        // Cập nhật rule trực tiếp bằng SQL
        $updated = Capsule::table(\'mod_addfundbonus_rules\')
            ->where(\'id\', $ruleId)
            ->update([
                \'min_amount\' => $minAmount,
                \'max_amount\' => $maxAmount,
                \'bonus_percent\' => $bonusPercent,
                \'description\' => $description,
                \'active\' => $active,
                \'updated_at\' => date(\'Y-m-d H:i:s\')
            ]);
            
        $message = $updated ? "Đã cập nhật rule ID: $ruleId thành công" : "Không có thay đổi cho rule ID: $ruleId";
        $alertType = $updated ? "alert-success" : "alert-warning";
    } catch (Exception $e) {
        $message = "Lỗi: " . $e->getMessage();
        $alertType = "alert-danger";
    }
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Cập nhật trực tiếp rule</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #0c5460; background-color: #d1ecf1; border-color: #bee5eb; }
        .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
        .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
        .alert-warning { color: #856404; background-color: #fff3cd; border-color: #ffeeba; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 8px 12px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn-secondary { background: #6c757d; }
        input, select { padding: 8px; width: 100%; box-sizing: border-box; }
        .form-group { margin-bottom: 15px; }
        h1, h2 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cập nhật trực tiếp rule</h1>
        
        <?php if (isset($message)): ?>
        <div class="alert <?php echo $alertType; ?>"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <h2>Danh sách Rules</h2>
        <table>
            <tr>
                <th>ID</th>
                <th>Min</th>
                <th>Max</th>
                <th>Bonus %</th>
                <th>Mô tả</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
            </tr>
            <?php
            $rules = Capsule::table(\'mod_addfundbonus_rules\')->get();
            foreach ($rules as $rule):
            ?>
            <tr>
                <td><?php echo $rule->id; ?></td>
                <td><?php echo $rule->min_amount; ?></td>
                <td><?php echo ($rule->max_amount > 0 ? $rule->max_amount : \'Không giới hạn\'); ?></td>
                <td><?php echo $rule->bonus_percent; ?>%</td>
                <td><?php echo $rule->description; ?></td>
                <td><?php echo ($rule->active ? \'Kích hoạt\' : \'Tắt\'); ?></td>
                <td><a href="?edit=<?php echo $rule->id; ?>" class="btn">Sửa</a></td>
            </tr>
            <?php endforeach; ?>
        </table>
        
        <?php
        if (isset($_GET[\'edit\']) && is_numeric($_GET[\'edit\'])):
            $ruleId = (int)$_GET[\'edit\'];
            $ruleToEdit = Capsule::table(\'mod_addfundbonus_rules\')
                ->where(\'id\', $ruleId)
                ->first();
                
            if ($ruleToEdit):
        ?>
        <h2>Cập nhật Rule ID: <?php echo $ruleToEdit->id; ?></h2>
        <form method="post" action="">
            <input type="hidden" name="update_rule" value="1">
            <input type="hidden" name="rule_id" value="<?php echo $ruleToEdit->id; ?>">
            
            <div class="form-group">
                <label>Số tiền tối thiểu:</label>
                <input type="number" name="min_amount" step="0.01" value="<?php echo $ruleToEdit->min_amount; ?>" required>
            </div>
            
            <div class="form-group">
                <label>Số tiền tối đa (0 = không giới hạn):</label>
                <input type="number" name="max_amount" step="0.01" value="<?php echo $ruleToEdit->max_amount; ?>">
            </div>
            
            <div class="form-group">
                <label>Phần trăm thưởng (%):</label>
                <input type="number" name="bonus_percent" step="0.01" value="<?php echo $ruleToEdit->bonus_percent; ?>" required>
            </div>
            
            <div class="form-group">
                <label>Mô tả:</label>
                <input type="text" name="description" value="<?php echo htmlspecialchars($ruleToEdit->description); ?>" required>
            </div>
            
            <div class="form-group">
                <label><input type="checkbox" name="active" <?php echo ($ruleToEdit->active ? \'checked\' : \'\'); ?>> Kích hoạt</label>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn">Cập nhật</button>
                <a href="<?php echo $_SERVER[\'PHP_SELF\']; ?>" class="btn btn-secondary" style="margin-left: 10px;">Hủy</a>
            </div>
        </form>
        <?php 
            else:
                echo "<div class=\'alert alert-danger\'>Không tìm thấy rule với ID: $ruleId</div>";
            endif;
        endif;
        ?>
        
        <div style="margin-top: 20px;">
            <a href="simplified_test.php" class="btn">Quay lại trang kiểm tra</a>
            <a href="check_update.php" class="btn">Kiểm tra cập nhật</a>
            <a href="addonmodules.php?module=addfundbonus" class="btn">Đi đến module</a>
        </div>
    </div>
</body>
</html>';

        file_put_contents($directUpdateFile, $directUpdateContent);
        writeLog("Đã tạo file công cụ cập nhật trực tiếp: $directUpdateFile");
    }
    
} catch (Exception $e) {
    writeLog("Lỗi: " . $e->getMessage());
    echo "<div class='alert alert-danger'>Lỗi: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "
        <div style='margin-top: 20px;'>
            <a href='simplified_test.php' class='btn'>Quay lại trang kiểm tra</a>
            <a href='check_update.php' class='btn'>Kiểm tra cập nhật</a>
            <a href='addonmodules.php?module=addfundbonus' class='btn'>Đi đến module</a>
        </div>
    </div>
</body>
</html>";
?> 