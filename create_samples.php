<?php
/**
 * File tạo rule mẫu cho AddFundBonus
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON>u cầu đăng nhập admin");
}

use Illuminate\Database\Capsule\Manager as Capsule;

// Ghi log
function writeLog($message) {
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [CREATE SAMPLES] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

// Thêm rules
try {
    // Rule 1
    $id1 = Capsule::table('mod_addfundbonus_rules')->insertGetId([
        'min_amount' => 100.00,
        'max_amount' => 500.00,
        'bonus_percent' => 5.00,
        'description' => 'Khuyến mãi 5% cho nạp từ 100-500',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    writeLog("Đã thêm rule 1: Khuyến mãi 5% cho nạp từ 100-500");
    
    // Rule 2
    $id2 = Capsule::table('mod_addfundbonus_rules')->insertGetId([
        'min_amount' => 500.01,
        'max_amount' => 1000.00,
        'bonus_percent' => 10.00,
        'description' => 'Khuyến mãi 10% cho nạp từ 500-1000',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    writeLog("Đã thêm rule 2: Khuyến mãi 10% cho nạp từ 500-1000");
    
    // Rule 3
    $id3 = Capsule::table('mod_addfundbonus_rules')->insertGetId([
        'min_amount' => 1000.01,
        'max_amount' => 0.00, // Không giới hạn
        'bonus_percent' => 15.00,
        'description' => 'Khuyến mãi 15% cho nạp trên 1000',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);
    
    writeLog("Đã thêm rule 3: Khuyến mãi 15% cho nạp trên 1000");
    
    // Thông báo thành công
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Thêm Rule Thành Công</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
            .alert-success { color: #155724; background-color: #d4edda; border-color: #c3e6cb; }
            .btn { display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        </style>
    </head>
    <body>
        <div class='alert alert-success'>
            <h2>Đã thêm thành công 3 rules mẫu!</h2>
            <p>Bây giờ bạn có thể sử dụng module Add Fund Bonus.</p>
        </div>
        
        <a href='simplified_test.php' class='btn'>Quay lại trang kiểm tra</a>
        <a href='addonmodules.php?module=addfundbonus' class='btn'>Đi đến module</a>
    </body>
    </html>";
    
} catch (Exception $e) {
    // Ghi log lỗi
    writeLog("Lỗi: " . $e->getMessage());
    
    // Hiển thị thông báo lỗi
    echo "<!DOCTYPE html>
    <html>
    <head>
        <title>Lỗi</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .alert { padding: 15px; margin-bottom: 20px; border: 1px solid transparent; border-radius: 4px; }
            .alert-danger { color: #721c24; background-color: #f8d7da; border-color: #f5c6cb; }
            .btn { display: inline-block; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
            pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow: auto; }
        </style>
    </head>
    <body>
        <div class='alert alert-danger'>
            <h2>Đã xảy ra lỗi:</h2>
            <p>" . $e->getMessage() . "</p>
        </div>
        
        <h3>Chi tiết lỗi:</h3>
        <pre>" . $e->getTraceAsString() . "</pre>
        
        <a href='simplified_test.php' class='btn'>Quay lại trang kiểm tra</a>
    </body>
    </html>";
}
?> 