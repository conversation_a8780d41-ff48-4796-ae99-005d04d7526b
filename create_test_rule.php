<?php
/**
 * Test script to create a rule directly in the database
 * Place this file in the root directory and access it from browser to create a test rule
 */

// Initialize WHMCS
require_once 'init.php';

// Check if user is admin
if (!isset($_SESSION['adminid'])) {
    die("Access Denied: Admin login required");
}

use Illuminate\Database\Capsule\Manager as Capsule;

echo "<h1>AddFundBonus Test Rule Creation</h1>";

// Function to log results
function logTestResult($message) {
    echo $message . "<br>";
    
    $logFile = ROOTDIR . '/modules/addons/addfundbonus/logs/addfundbonus.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [CREATE TEST] {$message}\n\n";
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

try {
    // Check if table exists
    $tableExists = Capsule::schema()->hasTable('mod_addfundbonus_rules');
    
    if (!$tableExists) {
        // Create table if it doesn't exist
        logTestResult("Table 'mod_addfundbonus_rules' does not exist. Creating now...");
        
        Capsule::schema()->create('mod_addfundbonus_rules', function ($table) {
            $table->increments('id');
            $table->decimal('min_amount', 10, 2)->default(0.00);
            $table->decimal('max_amount', 10, 2)->default(0.00);
            $table->decimal('bonus_percent', 5, 2)->default(0.00);
            $table->string('description', 255);
            $table->tinyInteger('active')->default(1);
            $table->timestamp('created_at')->default(Capsule::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(Capsule::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
        });
        
        logTestResult("Table 'mod_addfundbonus_rules' created successfully!");
    }

    // Create a test rule
    $testRule = [
        'min_amount' => 100.00,
        'max_amount' => 500.00,
        'bonus_percent' => 5.00,
        'description' => 'Test Rule (100-500)',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $id = Capsule::table('mod_addfundbonus_rules')->insertGetId($testRule);
    
    if ($id) {
        logTestResult("✅ Test rule created successfully! ID: {$id}");
        
        // Show the created rule
        $rule = Capsule::table('mod_addfundbonus_rules')->find($id);
        
        echo "<h2>Created Rule:</h2>";
        echo "<pre>";
        print_r($rule);
        echo "</pre>";
        
        echo "<p>Go to Admin Area -> Addons -> Add Fund Bonus to see the rule</p>";
    } else {
        logTestResult("❌ Failed to create test rule");
    }
    
    // Create second rule with different range
    $testRule2 = [
        'min_amount' => 500.01,
        'max_amount' => 1000.00,
        'bonus_percent' => 10.00,
        'description' => 'Test Rule (500-1000)',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $id2 = Capsule::table('mod_addfundbonus_rules')->insertGetId($testRule2);
    
    if ($id2) {
        logTestResult("✅ Second test rule created successfully! ID: {$id2}");
    }
    
    // Create third rule with unlimited range
    $testRule3 = [
        'min_amount' => 1000.01,
        'max_amount' => 0.00, // 0 means unlimited
        'bonus_percent' => 15.00,
        'description' => 'Test Rule (1000+)',
        'active' => 1,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    $id3 = Capsule::table('mod_addfundbonus_rules')->insertGetId($testRule3);
    
    if ($id3) {
        logTestResult("✅ Third test rule created successfully! ID: {$id3}");
    }
    
    // Show all rules
    $allRules = Capsule::table('mod_addfundbonus_rules')->get();
    
    echo "<h2>All Rules in Database:</h2>";
    echo "<pre>";
    print_r($allRules);
    echo "</pre>";
    
} catch (Exception $e) {
    logTestResult("❌ Error: " . $e->getMessage());
    echo "<pre>";
    print_r($e->getTraceAsString());
    echo "</pre>";
}
?> 