<?php
/**
 * Create tables and sample rules directly with SQL
 */

// Kết nối WHMCS
require_once 'init.php';

// Kiểm tra admin
if (!isset($_SESSION['adminid'])) {
    die("<PERSON><PERSON>u cầu đăng nhập admin");
}

// Lấy thông tin kết nối từ configuration.php
$db_host = $db_host ?? '';
$db_username = $db_username ?? '';
$db_password = $db_password ?? '';
$db_name = $db_name ?? '';

echo "<h1>Tạo bảng và mẫu cho AddFundBonus</h1>";

// Kết nối trực tiếp
$conn = new mysqli($db_host, $db_username, $db_password, $db_name);

// Kiểm tra kết nối
if ($conn->connect_error) {
    die("Lỗi kết nối: " . $conn->connect_error);
}

echo "Kế<PERSON> n<PERSON>i thành công đến cơ sở dữ liệu.<br>";

// SQL tạo bảng rules
$sql_create_rules = "CREATE TABLE IF NOT EXISTS `mod_addfundbonus_rules` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `min_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
    `max_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
    `bonus_percent` DECIMAL(5,2) NOT NULL DEFAULT '0.00',
    `description` VARCHAR(255) NOT NULL,
    `active` TINYINT(1) NOT NULL DEFAULT '1',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;";

// SQL tạo bảng logs
$sql_create_logs = "CREATE TABLE IF NOT EXISTS `mod_addfundbonus_logs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `client_id` INT(11) NOT NULL,
    `transaction_id` INT(11) NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
    `bonus_percent` DECIMAL(5,2) NOT NULL DEFAULT '0.00',
    `bonus_amount` DECIMAL(10,2) NOT NULL DEFAULT '0.00',
    `rule_id` INT(11) NULL,
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;";

// Thực thi tạo bảng
if ($conn->query($sql_create_rules) === TRUE) {
    echo "Bảng rules đã được tạo thành công<br>";
} else {
    echo "Lỗi tạo bảng rules: " . $conn->error . "<br>";
}

if ($conn->query($sql_create_logs) === TRUE) {
    echo "Bảng logs đã được tạo thành công<br>";
} else {
    echo "Lỗi tạo bảng logs: " . $conn->error . "<br>";
}

// Kiểm tra xem đã có rule nào chưa
$result = $conn->query("SELECT COUNT(*) as count FROM mod_addfundbonus_rules");
$row = $result->fetch_assoc();
$count = $row['count'];

echo "Hiện tại có $count rules trong hệ thống.<br>";

// Nếu chưa có rule nào, thêm một số rule mẫu
if ($count == 0) {
    // Mẫu 1: Khuyến mãi 5% cho nạp từ 100-500
    $sql1 = "INSERT INTO mod_addfundbonus_rules (min_amount, max_amount, bonus_percent, description, active) VALUES (100, 500, 5, 'Khuyến mãi 5% cho nạp từ 100-500', 1)";
    
    // Mẫu 2: Khuyến mãi 10% cho nạp từ 500-1000
    $sql2 = "INSERT INTO mod_addfundbonus_rules (min_amount, max_amount, bonus_percent, description, active) VALUES (500.01, 1000, 10, 'Khuyến mãi 10% cho nạp từ 500-1000', 1)";
    
    // Mẫu 3: Khuyến mãi 15% cho nạp trên 1000
    $sql3 = "INSERT INTO mod_addfundbonus_rules (min_amount, max_amount, bonus_percent, description, active) VALUES (1000.01, 0, 15, 'Khuyến mãi 15% cho nạp trên 1000', 1)";
    
    // Thực thi thêm rule
    if ($conn->query($sql1) === TRUE) {
        echo "Đã thêm rule 1<br>";
    } else {
        echo "Lỗi thêm rule 1: " . $conn->error . "<br>";
    }
    
    if ($conn->query($sql2) === TRUE) {
        echo "Đã thêm rule 2<br>";
    } else {
        echo "Lỗi thêm rule 2: " . $conn->error . "<br>";
    }
    
    if ($conn->query($sql3) === TRUE) {
        echo "Đã thêm rule 3<br>";
    } else {
        echo "Lỗi thêm rule 3: " . $conn->error . "<br>";
    }
    
    echo "Đã thêm 3 rule mẫu vào hệ thống.<br>";
} else {
    echo "Đã có rule trong hệ thống, không cần thêm mẫu.<br>";
}

// Hiển thị tất cả rule đã có
$result = $conn->query("SELECT * FROM mod_addfundbonus_rules");

echo "<h2>Danh sách rule hiện tại:</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Min</th><th>Max</th><th>Bonus %</th><th>Mô tả</th><th>Trạng thái</th></tr>";

while($row = $result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>".$row['id']."</td>";
    echo "<td>".$row['min_amount']."</td>";
    echo "<td>".($row['max_amount'] > 0 ? $row['max_amount'] : 'Không giới hạn')."</td>";
    echo "<td>".$row['bonus_percent']."%</td>";
    echo "<td>".$row['description']."</td>";
    echo "<td>".($row['active'] == 1 ? 'Kích hoạt' : 'Tắt')."</td>";
    echo "</tr>";
}

echo "</table>";

// Kiểm tra module đã được kích hoạt chưa
$result = $conn->query("SELECT * FROM tbladdonmodules WHERE module = 'addfundbonus'");

echo "<h2>Trạng thái module:</h2>";

if ($result->num_rows > 0) {
    echo "Module addfundbonus đã được kích hoạt.<br>";
    
    echo "<table border='1'>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>".$row['setting']."</td>";
        echo "<td>".$row['value']."</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "Module addfundbonus chưa được kích hoạt. Hãy vào Admin > Setup > Addon Modules để kích hoạt.<br>";
    
    // Kiểm tra xem module có tồn tại
    if (is_dir(ROOTDIR . '/modules/addons/addfundbonus')) {
        echo "✅ Thư mục module tồn tại.<br>";
    } else {
        echo "❌ Thư mục module không tồn tại. Hãy kiểm tra lại cài đặt.<br>";
    }
}

// Đóng kết nối
$conn->close();

echo "<p>Hoàn tất. Hãy truy cập Admin Area > Addons > Add Fund Bonus để xem module.</p>";
?> 