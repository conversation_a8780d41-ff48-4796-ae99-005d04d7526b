<?php
use WHMCS\Database\Capsule;

require 'init.php';

$invoiceid = $_POST['invoiceid'];
if ($invoiceid) {
    // Kiểm tra xem hóa đơn có thể hủy không
    $invoiceData = localAPI('GetInvoice', array('invoiceid' => $invoiceid));
    if ($invoiceData['status'] == 'Unpaid' || $invoiceData['status'] == 'Draft') {
        // Hủy hóa đơn
        $cancelResult = localAPI('UpdateInvoice', array('invoiceid' => $invoiceid, 'status' => 'Cancelled'));
        if ($cancelResult['result'] == 'success') {
            // Chạy hook sau khi hủy hóa đơn
            runPreModuleTerminateHook($invoiceid);
            header('Location: viewinvoice.php?id=' . $invoiceid . '&cancel=success');
        } else {
            header('Location: viewinvoice.php?id=' . $invoiceid . '&cancel=fail');
        }
    } else {
        header('Location: viewinvoice.php?id=' . $invoiceid . '&cancel=invalidstatus');
    }
} else {
    header('Location: clientarea.php');
}

function runPreModuleTerminateHook($invoiceid) {
    // Lấy danh sách dịch vụ liên quan đến hóa đơn
    $serviceIds = Capsule::table('tblinvoiceitems')
        ->where('invoiceid', $invoiceid)
        ->pluck('relid');

    foreach ($serviceIds as $serviceId) {
        // Gọi hook PreModuleTerminate cho từng dịch vụ
        $hookParams = array(
            'params' => array(
                'serviceid' => $serviceId
            )
        );
        run_hook('PreModuleTerminate', $hookParams);
    }
}
?>
